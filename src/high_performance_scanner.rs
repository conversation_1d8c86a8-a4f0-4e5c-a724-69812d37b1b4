use std::{
    collections::VecDeque,
    fs,
    path::{Path, PathBuf},
    sync::{
        atomic::{AtomicU64, AtomicUsize, Ordering},
        Arc, Mutex,
    },
    thread,
    time::{Duration, Instant},
};

use rayon::prelude::*;
use serde::{Deserialize, Serialize};

/// Configuration for the high-performance scanner
#[derive(Debug, <PERSON>lone)]
pub struct ScanConfig {
    /// Maximum number of threads to use for parallel scanning
    pub max_threads: usize,
    /// Minimum file size threshold for detailed tracking (bytes)
    pub min_file_size_threshold: u64,
    /// Maximum directory depth to scan
    pub max_depth: Option<usize>,
    /// Whether to follow symbolic links
    pub follow_symlinks: bool,
    /// Batch size for processing entries
    pub batch_size: usize,
    /// Whether to show progress during scanning
    pub show_progress: bool,
}

impl Default for ScanConfig {
    fn default() -> Self {
        Self {
            max_threads: num_cpus::get().max(1),
            min_file_size_threshold: 1024, // 1KB
            max_depth: None,
            follow_symlinks: false,
            batch_size: 1000,
            show_progress: true,
        }
    }
}

/// Statistics about the scanning process
#[derive(Debu<PERSON>, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct ScanStats {
    pub total_files: u64,
    pub total_directories: u64,
    pub total_size: u64,
    pub scan_duration: Duration,
    pub files_per_second: f64,
    pub bytes_per_second: f64,
}

/// Represents a file or directory entry with size information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DiskEntry {
    pub name: String,
    pub path: PathBuf,
    pub size: u64,
    pub is_directory: bool,
    pub children: Vec<DiskEntry>,
    pub file_count: u64,
    pub directory_count: u64,
}

impl DiskEntry {
    fn new(path: PathBuf, is_directory: bool) -> Self {
        let name = path
            .file_name()
            .map(|n| n.to_string_lossy().to_string())
            .unwrap_or_else(|| path.to_string_lossy().to_string());

        Self {
            name,
            path,
            size: 0,
            is_directory,
            children: Vec::new(),
            file_count: 0,
            directory_count: 0,
        }
    }

    /// Calculate total size including all children
    pub fn calculate_total_size(&mut self) {
        if self.is_directory {
            self.size = self.children.iter().map(|child| child.size).sum();
            self.file_count = self.children.iter().map(|child| child.file_count).sum();
            self.directory_count = self.children.iter().map(|child| child.directory_count).sum();
            if !self.children.is_empty() {
                self.directory_count += 1; // Count this directory
            }
        } else {
            self.file_count = 1;
            self.directory_count = 0;
        }
    }

    /// Sort children by size in descending order
    pub fn sort_children_by_size(&mut self) {
        self.children.sort_by(|a, b| b.size.cmp(&a.size));
        for child in &mut self.children {
            child.sort_children_by_size();
        }
    }

    /// Filter out small files/directories below threshold
    pub fn filter_small_entries(&mut self, threshold: u64) {
        if self.is_directory {
            let mut other_size = 0u64;
            let mut other_files = 0u64;
            let mut other_dirs = 0u64;

            self.children.retain(|child| {
                if child.size >= threshold {
                    true
                } else {
                    other_size += child.size;
                    other_files += child.file_count;
                    other_dirs += child.directory_count;
                    false
                }
            });

            // Add aggregated small entries as a single "*other*" entry
            if other_size > 0 {
                let mut other_entry = DiskEntry::new(
                    self.path.join("*other*"),
                    true,
                );
                other_entry.size = other_size;
                other_entry.file_count = other_files;
                other_entry.directory_count = other_dirs;
                self.children.push(other_entry);
            }

            // Recursively filter children
            for child in &mut self.children {
                child.filter_small_entries(threshold);
            }
        }
    }
}

/// High-performance directory scanner with parallel processing
pub struct HighPerformanceScanner {
    config: ScanConfig,
    stats: Arc<Mutex<ScanStats>>,
    files_processed: Arc<AtomicU64>,
    dirs_processed: Arc<AtomicU64>,
    bytes_processed: Arc<AtomicU64>,
    active_threads: Arc<AtomicUsize>,
}

impl HighPerformanceScanner {
    /// Create a new scanner with default configuration
    pub fn new() -> Self {
        Self::with_config(ScanConfig::default())
    }

    /// Create a new scanner with custom configuration
    pub fn with_config(config: ScanConfig) -> Self {
        Self {
            config,
            stats: Arc::new(Mutex::new(ScanStats {
                total_files: 0,
                total_directories: 0,
                total_size: 0,
                scan_duration: Duration::from_secs(0),
                files_per_second: 0.0,
                bytes_per_second: 0.0,
            })),
            files_processed: Arc::new(AtomicU64::new(0)),
            dirs_processed: Arc::new(AtomicU64::new(0)),
            bytes_processed: Arc::new(AtomicU64::new(0)),
            active_threads: Arc::new(AtomicUsize::new(0)),
        }
    }

    /// Scan a directory recursively and return the disk usage tree
    pub fn scan<P: AsRef<Path>>(&self, root_path: P) -> anyhow::Result<DiskEntry> {
        let start_time = Instant::now();
        let root_path = root_path.as_ref().to_path_buf();

        // Reset counters
        self.files_processed.store(0, Ordering::Relaxed);
        self.dirs_processed.store(0, Ordering::Relaxed);
        self.bytes_processed.store(0, Ordering::Relaxed);

        // Start progress monitoring if enabled
        let progress_handle = if self.config.show_progress {
            Some(self.start_progress_monitor())
        } else {
            None
        };

        // Perform the scan
        let result = self.scan_recursive(&root_path, 0);

        // Stop progress monitoring
        if let Some(handle) = progress_handle {
            handle.join().unwrap_or(());
        }

        let scan_duration = start_time.elapsed();
        let total_files = self.files_processed.load(Ordering::Relaxed);
        let total_dirs = self.dirs_processed.load(Ordering::Relaxed);
        let total_bytes = self.bytes_processed.load(Ordering::Relaxed);

        // Update statistics
        {
            let mut stats = self.stats.lock().unwrap();
            stats.total_files = total_files;
            stats.total_directories = total_dirs;
            stats.total_size = total_bytes;
            stats.scan_duration = scan_duration;
            stats.files_per_second = total_files as f64 / scan_duration.as_secs_f64();
            stats.bytes_per_second = total_bytes as f64 / scan_duration.as_secs_f64();
        }

        result
    }

    /// Get the current scan statistics
    pub fn get_stats(&self) -> ScanStats {
        self.stats.lock().unwrap().clone()
    }

    /// Internal recursive scanning method
    fn scan_recursive(&self, path: &Path, depth: usize) -> anyhow::Result<DiskEntry> {
        // Check depth limit
        if let Some(max_depth) = self.config.max_depth {
            if depth > max_depth {
                return Ok(DiskEntry::new(path.to_path_buf(), true));
            }
        }

        let metadata = match fs::metadata(path) {
            Ok(metadata) => metadata,
            Err(e) => {
                eprintln!("Failed to read metadata for {:?}: {}", path, e);
                return Ok(DiskEntry::new(path.to_path_buf(), false));
            }
        };

        if metadata.is_file() {
            let size = metadata.len();
            self.files_processed.fetch_add(1, Ordering::Relaxed);
            self.bytes_processed.fetch_add(size, Ordering::Relaxed);

            let mut entry = DiskEntry::new(path.to_path_buf(), false);
            entry.size = size;
            entry.calculate_total_size();
            return Ok(entry);
        }

        if !metadata.is_dir() {
            return Ok(DiskEntry::new(path.to_path_buf(), false));
        }

        self.dirs_processed.fetch_add(1, Ordering::Relaxed);

        // Read directory entries
        let entries = match fs::read_dir(path) {
            Ok(entries) => entries,
            Err(e) => {
                eprintln!("Failed to read directory {:?}: {}", path, e);
                return Ok(DiskEntry::new(path.to_path_buf(), true));
            }
        };

        // Collect all valid entries
        let mut paths_to_scan = Vec::new();
        for entry in entries {
            match entry {
                Ok(entry) => {
                    let entry_path = entry.path();

                    // Skip if it's a symlink and we're not following them
                    if !self.config.follow_symlinks {
                        if let Ok(file_type) = entry.file_type() {
                            if file_type.is_symlink() {
                                continue;
                            }
                        }
                    }

                    paths_to_scan.push(entry_path);
                }
                Err(e) => {
                    eprintln!("Failed to read directory entry in {:?}: {}", path, e);
                }
            }
        }

        // Process entries in parallel using rayon
        let children: Vec<DiskEntry> = if paths_to_scan.len() > self.config.batch_size {
            // For large directories, process in parallel batches
            paths_to_scan
                .par_chunks(self.config.batch_size)
                .flat_map(|chunk| {
                    chunk
                        .par_iter()
                        .filter_map(|entry_path| {
                            self.active_threads.fetch_add(1, Ordering::Relaxed);
                            let result = self.scan_recursive(entry_path, depth + 1).ok();
                            self.active_threads.fetch_sub(1, Ordering::Relaxed);
                            result
                        })
                        .collect::<Vec<_>>()
                })
                .collect()
        } else {
            // For smaller directories, process in parallel directly
            paths_to_scan
                .par_iter()
                .filter_map(|entry_path| {
                    self.active_threads.fetch_add(1, Ordering::Relaxed);
                    let result = self.scan_recursive(entry_path, depth + 1).ok();
                    self.active_threads.fetch_sub(1, Ordering::Relaxed);
                    result
                })
                .collect()
        };

        let mut entry = DiskEntry::new(path.to_path_buf(), true);
        entry.children = children;
        entry.calculate_total_size();

        Ok(entry)
    }

    /// Start a progress monitoring thread
    fn start_progress_monitor(&self) -> thread::JoinHandle<()> {
        let files_processed = Arc::clone(&self.files_processed);
        let dirs_processed = Arc::clone(&self.dirs_processed);
        let bytes_processed = Arc::clone(&self.bytes_processed);
        let active_threads = Arc::clone(&self.active_threads);

        thread::spawn(move || {
            let start_time = Instant::now();
            loop {
                thread::sleep(Duration::from_secs(1));

                let files = files_processed.load(Ordering::Relaxed);
                let dirs = dirs_processed.load(Ordering::Relaxed);
                let bytes = bytes_processed.load(Ordering::Relaxed);
                let threads = active_threads.load(Ordering::Relaxed);
                let elapsed = start_time.elapsed().as_secs_f64();

                let files_per_sec = files as f64 / elapsed;
                let mb_per_sec = (bytes as f64 / elapsed) / (1024.0 * 1024.0);

                print!("\rScanning... Files: {}, Dirs: {}, Size: {:.2} MB, Speed: {:.0} files/s, {:.2} MB/s, Threads: {}",
                    files, dirs, bytes as f64 / (1024.0 * 1024.0), files_per_sec, mb_per_sec, threads);

                use std::io::{self, Write};
                io::stdout().flush().unwrap();

                // Check if scanning is likely complete (no active threads for a while)
                if threads == 0 && elapsed > 1.0 {
                    thread::sleep(Duration::from_millis(100));
                    if active_threads.load(Ordering::Relaxed) == 0 {
                        break;
                    }
                }
            }
            println!(); // New line after progress
        })
    }

    /// Scan and return a processed result with filtering and sorting applied
    pub fn scan_and_process<P: AsRef<Path>>(&self, root_path: P) -> anyhow::Result<DiskEntry> {
        let mut result = self.scan(root_path)?;

        // Apply filtering for small entries
        result.filter_small_entries(self.config.min_file_size_threshold);

        // Sort by size
        result.sort_children_by_size();

        Ok(result)
    }

    /// Quick scan that only returns summary statistics without building the tree
    pub fn quick_scan<P: AsRef<Path>>(&self, root_path: P) -> anyhow::Result<ScanStats> {
        let start_time = Instant::now();
        let root_path = root_path.as_ref();

        // Reset counters
        self.files_processed.store(0, Ordering::Relaxed);
        self.dirs_processed.store(0, Ordering::Relaxed);
        self.bytes_processed.store(0, Ordering::Relaxed);

        // Use a work-stealing queue for better performance
        let mut queue = VecDeque::new();
        queue.push_back(root_path.to_path_buf());

        while let Some(current_path) = queue.pop_front() {
            if let Ok(metadata) = fs::metadata(&current_path) {
                if metadata.is_file() {
                    self.files_processed.fetch_add(1, Ordering::Relaxed);
                    self.bytes_processed.fetch_add(metadata.len(), Ordering::Relaxed);
                } else if metadata.is_dir() {
                    self.dirs_processed.fetch_add(1, Ordering::Relaxed);

                    if let Ok(entries) = fs::read_dir(&current_path) {
                        for entry in entries.flatten() {
                            queue.push_back(entry.path());
                        }
                    }
                }
            }
        }

        let scan_duration = start_time.elapsed();
        let total_files = self.files_processed.load(Ordering::Relaxed);
        let total_dirs = self.dirs_processed.load(Ordering::Relaxed);
        let total_bytes = self.bytes_processed.load(Ordering::Relaxed);

        Ok(ScanStats {
            total_files,
            total_directories: total_dirs,
            total_size: total_bytes,
            scan_duration,
            files_per_second: total_files as f64 / scan_duration.as_secs_f64(),
            bytes_per_second: total_bytes as f64 / scan_duration.as_secs_f64(),
        })
    }
}

impl Default for HighPerformanceScanner {
    fn default() -> Self {
        Self::new()
    }
}

/// Utility functions for formatting and displaying results
impl DiskEntry {
    /// Format size in human-readable format
    pub fn format_size(&self) -> String {
        format_bytes(self.size)
    }

    /// Get the top N largest entries
    pub fn get_top_entries(&self, n: usize) -> Vec<&DiskEntry> {
        let mut entries: Vec<&DiskEntry> = if self.is_directory {
            self.children.iter().collect()
        } else {
            vec![self]
        };

        entries.sort_by(|a, b| b.size.cmp(&a.size));
        entries.into_iter().take(n).collect()
    }

    /// Print a tree representation of the disk usage
    pub fn print_tree(&self, max_depth: usize) {
        self.print_tree_recursive(0, max_depth, "");
    }

    fn print_tree_recursive(&self, current_depth: usize, max_depth: usize, prefix: &str) {
        if current_depth > max_depth {
            return;
        }

        let size_str = format_bytes(self.size);
        let type_indicator = if self.is_directory { "📁" } else { "📄" };

        println!("{}{} {} ({})", prefix, type_indicator, self.name, size_str);

        if self.is_directory && current_depth < max_depth {
            for (i, child) in self.children.iter().enumerate() {
                let is_last = i == self.children.len() - 1;
                let child_prefix = format!("{}{}",
                    prefix,
                    if is_last { "└── " } else { "├── " }
                );
                let next_prefix = format!("{}{}",
                    prefix,
                    if is_last { "    " } else { "│   " }
                );

                print!("{}", child_prefix);
                child.print_tree_recursive(current_depth + 1, max_depth, &next_prefix);
            }
        }
    }
}

/// Format bytes in human-readable format (B, KB, MB, GB, TB)
fn format_bytes(bytes: u64) -> String {
    const UNITS: &[&str] = &["B", "KB", "MB", "GB", "TB", "PB"];
    const THRESHOLD: f64 = 1024.0;

    if bytes == 0 {
        return "0 B".to_string();
    }

    let bytes_f = bytes as f64;
    let unit_index = (bytes_f.log10() / THRESHOLD.log10()).floor() as usize;
    let unit_index = unit_index.min(UNITS.len() - 1);

    let value = bytes_f / THRESHOLD.powi(unit_index as i32);

    if unit_index == 0 {
        format!("{} {}", bytes, UNITS[unit_index])
    } else {
        format!("{:.2} {}", value, UNITS[unit_index])
    }
}