pub mod scanner;
pub mod async_scanner;
pub mod pool_scanner;
pub mod high_performance_scanner;

use rayon::prelude::*;
use tokio::sync::Mutex;
use std::{
    collections::{HashMap, HashSet},
    fs,
    path::{Path, PathBuf},
    sync::{
        Arc,
        atomic::{AtomicBool, Ordering},
    },
    time::Instant,
};

use fs_extra::dir::get_size;
use rayon::{ThreadPool, ThreadPoolBuilder};

use crate::{async_scanner::AsyncScanner, pool_scanner::PoolScanner, scanner::{ParallelScanner, ScanFileInfo}};

#[tokio::main]
async fn main() -> anyhow::Result<()> {
    // let s = fs::read_dir("/Users/<USER>")?.filter_map(|entry| entry.ok()).collect::<Vec<_>>();
    // let r = s.iter().filter_map(|entry| {
    //     let path = entry.path();
    //     let size = get_size(&path);
    //     match size {
    //         Ok(size) => {
    //             Some((path, size))
    //         },
    //         _ => None,
    //     }
    // }).collect::<Vec<_>>();

    // println!("{:#?}", r);
    let path = PathBuf::from("/Users/<USER>/code");

    // {
    //     let start = Instant::now();

    //     let s = get_size(&path).ok();
    //     println!("size: {:?}", s);

    //     println!("{:?}", start.elapsed());
    // }
    {
            let start = Instant::now();

            // parallel
            let scanner = ParallelScanner::instance();
            ParallelScanner::scan_directory(scanner, &path);


            // let stat = scanner.lock().unwrap().get_stat(&path);
            // if let Ok(json) = serde_json::to_string(&stat) {
            // }

            // let x = scanner.lock().unwrap();
            // println!("size_map len: {}", x.size_map.len());


            // async 
            // let async_scanner = Arc::new(Mutex::new(async_scanner::AsyncScanner::new()));
            // AsyncScanner::scan_directory(async_scanner.clone(), path).await;

            // pool
            // let scanner = PoolScanner::instance();
            // PoolScanner::scan_directory(scanner, &path);
            // PoolScanner::test();


            println!("{:?}", start.elapsed());
    }

    Ok(())
}

struct DiskStat {
    name: String,
    size: Option<u64>,
    children: Vec<DiskStat>,
}

struct DiskScanner {
    size_map: HashMap<PathBuf, u64>,
    path_map: HashMap<PathBuf, HashSet<PathBuf>>,
    pool: Arc<ThreadPool>,
    should_stop: Arc<AtomicBool>,
}

impl DiskScanner {
    fn new() -> Self {
        let pool = ThreadPoolBuilder::new()
            .num_threads(num_cpus::get())
            .build()
            .unwrap();

        Self {
            size_map: HashMap::new(),
            path_map: HashMap::new(),
            pool: Arc::new(pool),
            should_stop: Arc::new(AtomicBool::new(false)),
        }
    }

    pub fn scan(&mut self, path: &PathBuf) {
        self.should_stop.store(false, Ordering::Relaxed);
        let pool = self.pool.clone();
        // pool.install(|| {
        //     self.scan_directory2(path);
        // })
        // self.scan_directory(path).await;
        // tokio::runtime::Runtime::new().unwrap().block_on(async {
        //     self.scan_directory(path);
        // });
    }

    fn scan_directory(&mut self, path: &PathBuf) -> anyhow::Result<()> {
        let result = fs::read_dir(path)?;
        let entries = result.filter_map(|entry| entry.ok()).collect::<Vec<_>>();
        let result = entries
            .par_iter()
            .filter_map(|entry| match entry.file_type() {
                Ok(file_type) if file_type.is_file() => match entry.metadata() {
                    Ok(metadata) => Some((entry.path(), ScanFileInfo::File(metadata.len()))),
                    Err(err) => {
                        println!("{:?}", err);
                        None
                    }
                },
                Ok(file_type) if file_type.is_dir() => {
                    Some((entry.path(), ScanFileInfo::Directory))
                }
                Err(error) => None,
                Ok(f) => {
                    // println!("{:?}", f);
                    None
                }
            })
            .fold(
                || (Vec::new(), Vec::new()),
                |(mut files, mut dirs), (path, info)| {
                    match info {
                        ScanFileInfo::File(size) => {
                            files.push((path.clone(), size));
                        }
                        ScanFileInfo::Directory => {
                            dirs.push(path.clone());
                        }
                    }
                    (files, dirs)
                },
            )
            .reduce(
                || (Vec::new(), Vec::new()),
                |(mut files, mut dirs), (mut files2, mut dirs2)| {
                    files.extend(files2);
                    dirs.extend(dirs2);
                    (files, dirs)
                },
            );

        let (files, directories) = result;
        // self.path_map.insert(path.clone(), );

        // self.size_map
        //     .extend(files.iter().map(|(path, size)| (path.clone(), *size)));

        // let children_paths: HashSet<_> = directories
        //     .iter()
        //     .chain(files.iter().map(|(path, _)| path))
        //     .cloned()
        //     .collect();
        // self.path_map.insert(path.clone(), children_paths);

        // directories.iter().for_each(|path| {
        //     let pool = self.pool.clone();
        //     pool.install(|| self.scan_directory(path));
        // });

        // directories.par_iter().for_each(|path| {
        //     self.scan_directory(path);
        // });



        Ok(())
    }
}
