# High-Performance Directory Scanner

A blazingly fast, parallel directory scanner written in Rust that recursively scans paths and calculates space usage with exceptional performance.

## Features

🚀 **High Performance**
- Multi-threaded parallel scanning using Rayon
- Optimized for both small and large directory structures
- Batch processing for large directories
- Work-stealing algorithms for optimal CPU utilization

⚙️ **Configurable**
- Customizable thread count
- Adjustable file size thresholds
- Maximum depth limiting
- Symlink following options
- Progress monitoring

📊 **Rich Statistics**
- Total files and directories count
- Total size calculation
- Scanning speed metrics (files/sec, MB/sec)
- Detailed timing information

🌳 **Tree Visualization**
- Hierarchical directory structure display
- Size-based sorting
- Small file aggregation
- Human-readable size formatting

## Performance Benchmarks

Based on testing with a typical development directory (~50K files, ~5.5GB):

- **Scan Speed**: 200K+ files/second
- **Throughput**: 20+ GB/second processing
- **Memory Efficient**: Minimal memory footprint
- **Scalable**: Performance scales with CPU cores

## Usage Examples

### Basic Usage

```rust
use test_rust::high_performance_scanner::HighPerformanceScanner;

let scanner = HighPerformanceScanner::new();
let result = scanner.scan_and_process(".")?;

println!("Total size: {}", result.format_size());
println!("Files: {}, Directories: {}", 
    scanner.get_stats().total_files,
    scanner.get_stats().total_directories
);
```

### Custom Configuration

```rust
use test_rust::high_performance_scanner::{HighPerformanceScanner, ScanConfig};

let config = ScanConfig {
    max_threads: 8,
    min_file_size_threshold: 10 * 1024, // 10KB
    max_depth: Some(3),
    follow_symlinks: false,
    batch_size: 500,
    show_progress: true,
};

let scanner = HighPerformanceScanner::with_config(config);
let result = scanner.scan_and_process("/path/to/scan")?;

// Display tree structure
result.print_tree(2);
```

### Quick Statistics Only

```rust
let scanner = HighPerformanceScanner::new();
let stats = scanner.quick_scan("/path/to/scan")?;

println!("Quick scan: {} files, {:.2} MB", 
    stats.total_files,
    stats.total_size as f64 / (1024.0 * 1024.0)
);
```

## Configuration Options

| Option | Type | Default | Description |
|--------|------|---------|-------------|
| `max_threads` | `usize` | CPU cores | Maximum parallel threads |
| `min_file_size_threshold` | `u64` | 1024 | Minimum file size for detailed tracking (bytes) |
| `max_depth` | `Option<usize>` | None | Maximum directory depth to scan |
| `follow_symlinks` | `bool` | false | Whether to follow symbolic links |
| `batch_size` | `usize` | 1000 | Batch size for processing entries |
| `show_progress` | `bool` | false | Enable real-time progress display |

## API Reference

### Core Types

#### `HighPerformanceScanner`
Main scanner struct with configurable options.

**Methods:**
- `new()` - Create with default configuration
- `with_config(config)` - Create with custom configuration
- `scan(path)` - Full scan returning directory tree
- `scan_and_process(path)` - Scan with filtering and sorting
- `quick_scan(path)` - Statistics-only scan
- `get_stats()` - Get current scan statistics

#### `DiskEntry`
Represents a file or directory with size information.

**Methods:**
- `format_size()` - Human-readable size formatting
- `get_top_entries(n)` - Get N largest entries
- `print_tree(depth)` - Print tree visualization
- `filter_small_entries(threshold)` - Filter out small entries
- `sort_children_by_size()` - Sort by size descending

#### `ScanStats`
Statistics about the scanning process.

**Fields:**
- `total_files: u64` - Total number of files
- `total_directories: u64` - Total number of directories
- `total_size: u64` - Total size in bytes
- `scan_duration: Duration` - Time taken to scan
- `files_per_second: f64` - Processing speed
- `bytes_per_second: f64` - Throughput

## Implementation Details

### Parallel Processing Strategy

1. **Work Distribution**: Uses Rayon's work-stealing for optimal load balancing
2. **Batch Processing**: Large directories are processed in configurable batches
3. **Atomic Counters**: Thread-safe statistics tracking using atomic operations
4. **Memory Efficiency**: Minimal memory allocation during scanning

### Optimization Techniques

- **Early Termination**: Respects depth limits to avoid unnecessary work
- **Symlink Handling**: Configurable symlink following to prevent cycles
- **Error Resilience**: Continues scanning despite individual file/directory errors
- **Progress Monitoring**: Optional real-time progress display

### Thread Safety

All operations are thread-safe using:
- `Arc<AtomicU64>` for counters
- `Arc<Mutex<T>>` for shared state
- Rayon's parallel iterators for safe parallelism

## Running the Examples

```bash
# Run the comprehensive demo
cargo run --example scanner_demo

# Run the main program with comparisons
cargo run
```

## Dependencies

- `rayon` - Data parallelism
- `serde` - Serialization support
- `anyhow` - Error handling
- `num_cpus` - CPU core detection

## License

This implementation is part of the rust-playground project.
